'use client';
import { useState, useEffect, useContext, useCallback, useMemo } from 'react';
import { Box, Typography } from '@mui/material';
import FilterCollapse from '@/components/UI/FilterCollapse/FilterCollapse';
import CommonTable from '@/components/UI/CommonTable/CommonTable';

import ContentLoader from '@/components/UI/ContentLoader';
import AuthContext from '@/helper/authcontext';
import { setApiMessage } from '@/helper/common/commonFunctions';
import { leaveService } from '@/services/leaveService';
import { staticOptions } from '@/helper/common/staticOptions';
import BranchDepartmentDisplay from '@/components/BranchDepartmentDisplay';
import { branchService } from '@/services/branchService';
import '../reports.scss';

export default function LeaveBalanceReports() {
  const { authState, AllListsData, setAllListsData } = useContext(AuthContext);

  // State management
  const [loader, setLoader] = useState(false);
  const [leaveBalanceList, setLeaveBalanceList] = useState([]);
  const [totalCount, setTotalCount] = useState(0);
  const [page, setPage] = useState(1);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [filters, setFilters] = useState({});
  const [searchValue, setSearchValue] = useState('');
  const [filterFieldsWithOptions, setFilterFieldsWithOptions] = useState([]);
  const [roleList, setRoleList] = useState([]);
  const [sortOrder, setSortOrder] = useState({ key: '', value: 'ASC' });

  // Filter fields configuration
  const filterFields = useMemo(
    () => [
      {
        type: 'search',
        label: 'Search',
        name: 'search',
        placeholder: 'Enter Search',
      },
      {
        type: 'select',
        label: 'Branch',
        name: 'branch',
        placeholder: 'Select Branch',
        options: AllListsData?.ActiveBranchList || [],
        showDot: true,
      },
      {
        type: 'select',
        label: 'Department',
        name: 'department',
        placeholder: 'Select Department',
        options: AllListsData?.ActiveDepartmentList || [],
      },
      {
        type: 'select',
        label: 'System Access',
        name: 'role',
        placeholder: 'Select Role',
        options: roleList,
      },
      {
        type: 'select',
        label: 'Leave Type',
        name: 'leavetype',
        placeholder: 'Select Leave Type',
        options: staticOptions?.LEAVE_TYPE || [],
      },
      {
        type: 'date-range',
        label: 'Date Range',
        name: 'dateRange',
        placeholder: 'Select date range',
        format: 'MMM dd, yyyy',
      },
    ],
    [
      AllListsData?.ActiveBranchList,
      AllListsData?.ActiveDepartmentList,
      roleList,
    ]
  );

  // No action menu items needed for leave balance reports

  // CommonTable columns configuration (matching first image exactly)
  const columns = [
    {
      header: 'ID',
      accessor: 'user_id',
      sortable: true, // Added sorting to ID as requested
      renderCell: (value) => (
        <Box className="d-flex align-center justify-center h100">
          {value ? value : '-'}
        </Box>
      ),
    },
    {
      header: 'User',
      accessor: 'user_first_name',
      sortable: false,
      renderCell: (_, row) => (
        <Box className="d-flex align-center gap-10 h100">
          <Box className="user-avatar-wrapper">
            {row?.user_profile_image ? (
              <img
                src={row.user_profile_image}
                alt="User"
                className="user-avatar-img"
                style={{ width: '32px', height: '32px', borderRadius: '50%' }}
              />
            ) : (
              <Box
                className="user-avatar-placeholder"
                style={{
                  width: '32px',
                  height: '32px',
                  borderRadius: '50%',
                  backgroundColor: '#1976d2',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  color: 'white',
                  fontSize: '14px',
                  fontWeight: 'bold',
                }}
              >
                {row?.user_first_name?.charAt(0)?.toUpperCase() || 'U'}
              </Box>
            )}
          </Box>
          <Box>
            <Typography className="title-text text-capital">
              {row?.user_first_name && row?.user_last_name
                ? `${row.user_first_name} ${row.user_last_name}`
                : row?.user_first_name || row?.user_last_name || '-'}
            </Typography>
            <Typography
              className="sub-title-text"
              style={{ fontSize: '12px', color: '#666' }}
            >
              {row?.user_email || '-'}
            </Typography>
          </Box>
        </Box>
      ),
    },
    {
      header: 'Branch / Dep.',
      accessor: 'branch_name',
      sortable: false,
      renderCell: (_, row) => (
        <Box className="d-flex align-center justify-center h100">
          <BranchDepartmentDisplay
            branchName={row?.branch_name}
            departmentName={row?.department_name}
            branchColor={row?.branch_color}
          />
        </Box>
      ),
    },
    {
      header: 'Total Leaves',
      accessor: 'total_leaves',
      sortable: false,
      renderCell: (value) => (
        <Box className="d-flex align-center justify-center h100">
          <Typography className="title-text">
            {value !== null && value !== undefined ? value : '0'}
          </Typography>
        </Box>
      ),
    },
    {
      header: 'Used Leave',
      accessor: 'leave_used',
      sortable: false,
      renderCell: (value) => (
        <Box className="d-flex align-center justify-center h100">
          <Typography className="title-text">
            {value !== null && value !== undefined ? value : '0'}
          </Typography>
        </Box>
      ),
    },
    {
      header: 'Balance',
      accessor: 'leave_balance',
      sortable: false,
      renderCell: (value) => (
        <Box className="d-flex align-center justify-center h100">
          <Typography className="title-text fw600">
            {value !== null && value !== undefined ? value : '0'}
          </Typography>
        </Box>
      ),
    },
  ];

  // Get leave balance list using service
  const getLeaveBalanceList = useCallback(
    async (
      search = '',
      pageNo = 1,
      branch = '',
      role = '',
      department = '',
      Rpp = rowsPerPage,
      startDate = '',
      endDate = '',
      leaveType = '',
      sortBy = '',
      sortOrderValue = '',
      showLoader = true
    ) => {
      if (showLoader) setLoader(true);
      try {
        const params = {
          search,
          page: pageNo,
          size: Rpp,
          branch_id: branch,
          department_id: department,
          role_id: role,
          start_date: startDate,
          end_date: endDate,
          report_mode: leaveType,
          sort_by: sortBy,
          sort_order: sortOrderValue,
        };

        const response = await leaveService.getLeaveBalanceList(params);

        if (response.success) {
          setLeaveBalanceList(response.data);
          setTotalCount(response.count);
        } else {
          setLeaveBalanceList([]);
          setTotalCount(0);
          setApiMessage('error', response.message);
        }
        setLoader(false);
      } catch (error) {
        setLoader(false);
        setLeaveBalanceList([]);
        setTotalCount(0);
        setApiMessage('error', 'Failed to fetch leave balance data');
      }
    },
    [rowsPerPage]
  );

  // Load filter options using service
  const loadFilterOptions = useCallback(async () => {
    try {
      const response = await leaveService.getRoleList();
      if (response.success) {
        const roleOptions =
          response.data?.map((role) => ({
            label: role.role_name,
            value: role.id,
          })) || [];
        setRoleList(roleOptions);
      }
    } catch (error) {
      console.error('Error loading filter options:', error);
    }
  }, []);

  // Handle sorting
  const handleSort = (key) => {
    const newOrder = sortOrder?.value === 'ASC' ? 'DESC' : 'ASC';
    const newSortOrder = { key, value: newOrder };
    setSortOrder(newSortOrder);
    setPage(1);

    const startDate = filters?.dateRange?.[0]
      ? new Date(filters.dateRange[0]).toISOString().split('T')[0]
      : '';
    const endDate = filters?.dateRange?.[1]
      ? new Date(filters.dateRange[1]).toISOString().split('T')[0]
      : '';

    // Call API with new sort order without showing loader
    getLeaveBalanceList(
      searchValue,
      1,
      filters?.branch || '',
      filters?.role || '',
      filters?.department || '',
      rowsPerPage,
      startDate,
      endDate,
      filters?.leavetype || '',
      key,
      newOrder,
      false // Don't show loader for sorting
    );
  };

  // Load branch data for colors
  const loadBranchDataForColors = useCallback(async () => {
    try {
      const branchAndDeptData =
        await branchService.getBranchAndDepartmentLists();
      setAllListsData((prevData) => ({
        ...prevData,
        SelectBranchList: branchAndDeptData?.selectBranchList,
        ActiveBranchList: branchAndDeptData?.activeBranchList,
        SelectDepartmentList: branchAndDeptData?.selectDepartmentList,
        ActiveDepartmentList: branchAndDeptData?.activeDepartmentList,
      }));
    } catch (error) {
      console.error('Error loading branch data:', error);
    }
  }, [setAllListsData]);

  // Handle filter application
  const handleApplyFilters = (values) => {
    setFilters(values);
    setSearchValue(values?.search || '');

    const startDate = values?.dateRange?.[0]
      ? new Date(values.dateRange[0]).toISOString().split('T')[0]
      : '';
    const endDate = values?.dateRange?.[1]
      ? new Date(values.dateRange[1]).toISOString().split('T')[0]
      : '';

    setPage(1);
    getLeaveBalanceList(
      values?.search || '',
      1,
      values?.branch || '',
      values?.role || '',
      values?.department || '',
      rowsPerPage,
      startDate,
      endDate,
      values?.leavetype || '',
      sortOrder?.key || '',
      sortOrder?.value || ''
    );
  };

  // Handle pagination
  const handlePageChange = (newPage) => {
    setPage(newPage);
    const startDate = filters?.dateRange?.[0]
      ? new Date(filters.dateRange[0]).toISOString().split('T')[0]
      : '';
    const endDate = filters?.dateRange?.[1]
      ? new Date(filters.dateRange[1]).toISOString().split('T')[0]
      : '';

    getLeaveBalanceList(
      searchValue,
      newPage,
      filters?.branch || '',
      filters?.role || '',
      filters?.department || '',
      rowsPerPage,
      startDate,
      endDate,
      filters?.leavetype || '',
      sortOrder?.key || '',
      sortOrder?.value || ''
    );
  };

  const handleRowsPerPageChange = (newRowsPerPage) => {
    setRowsPerPage(newRowsPerPage);
    setPage(1);
    const startDate = filters?.dateRange?.[0]
      ? new Date(filters.dateRange[0]).toISOString().split('T')[0]
      : '';
    const endDate = filters?.dateRange?.[1]
      ? new Date(filters.dateRange[1]).toISOString().split('T')[0]
      : '';

    getLeaveBalanceList(
      searchValue,
      1,
      filters?.branch || '',
      filters?.role || '',
      filters?.department || '',
      newRowsPerPage,
      startDate,
      endDate,
      filters?.leavetype || '',
      sortOrder?.key || '',
      sortOrder?.value || ''
    );
  };

  // useEffect hooks
  useEffect(() => {
    loadFilterOptions();
    loadBranchDataForColors();
  }, [loadFilterOptions, loadBranchDataForColors]);

  useEffect(() => {
    getLeaveBalanceList();
  }, [getLeaveBalanceList]);

  // Update filter fields with loaded options
  useEffect(() => {
    setFilterFieldsWithOptions(filterFields);
  }, [filterFields]);

  return (
    <Box className="report-main-container">
      <FilterCollapse
        fields={filterFieldsWithOptions}
        onApplyFilters={handleApplyFilters}
        initialValues={filters}
      />

      <Box className="report-table-container">
        {loader ? (
          <ContentLoader />
        ) : (
          <CommonTable
            columns={columns}
            data={leaveBalanceList}
            totalCount={totalCount}
            page={page}
            rowsPerPage={rowsPerPage}
            onPageChange={handlePageChange}
            onRowsPerPageChange={handleRowsPerPageChange}
            showPagination={true}
            onSort={handleSort}
            sortOrder={sortOrder}
          />
        )}
      </Box>
    </Box>
  );
}
